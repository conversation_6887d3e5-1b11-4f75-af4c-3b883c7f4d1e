﻿{
	"SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ": [
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0001].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2020-12-19T09:16:51.826Z",
			"endingDate": "2020-12-19T09:16:51.826Z",
			"scheduledDate": "2020-12-19T09:16:51.826Z",
			"status": -1,
			"backupNumber": 1,
			"duration": 2225,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0002].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2020-12-21T14:47:32.442Z",
			"endingDate": "2020-12-21T14:47:32.442Z",
			"scheduledDate": "2020-12-21T14:47:32.442Z",
			"status": -1,
			"backupNumber": 2,
			"duration": 2431,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0003].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2020-12-24T10:45:25.828Z",
			"endingDate": "2020-12-24T10:45:25.828Z",
			"scheduledDate": "2020-12-24T10:45:25.828Z",
			"status": -1,
			"backupNumber": 3,
			"duration": 2438,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0004].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-01-07T16:54:11.406Z",
			"endingDate": "2021-01-07T16:54:11.406Z",
			"scheduledDate": "2021-01-07T16:54:11.406Z",
			"status": -1,
			"backupNumber": 4,
			"duration": 2203,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0005].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-01-20T06:59:14.305Z",
			"endingDate": "2021-01-20T06:59:14.305Z",
			"scheduledDate": "2021-01-20T06:59:14.305Z",
			"status": -1,
			"backupNumber": 5,
			"duration": 4105,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0006].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-02-02T09:44:31.845Z",
			"endingDate": "2021-02-02T09:44:31.845Z",
			"scheduledDate": "2021-02-02T09:44:31.845Z",
			"status": -1,
			"backupNumber": 6,
			"duration": 2360,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0007].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-02-03T10:34:45.977Z",
			"endingDate": "2021-02-03T10:34:45.977Z",
			"scheduledDate": "2021-02-03T10:34:45.977Z",
			"status": -1,
			"backupNumber": 7,
			"duration": 3707,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0008].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-02-03T10:34:54.190Z",
			"endingDate": "2021-02-03T10:34:54.190Z",
			"scheduledDate": "2021-02-03T10:34:54.190Z",
			"status": -1,
			"backupNumber": 8,
			"duration": 1518,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0009].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-02-10T08:57:21.772Z",
			"endingDate": "2021-02-10T08:57:21.772Z",
			"scheduledDate": "2021-02-10T08:57:21.772Z",
			"status": -1,
			"backupNumber": 9,
			"duration": 2770,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0010].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-02-11T16:30:19.010Z",
			"endingDate": "2021-02-11T16:30:19.010Z",
			"scheduledDate": "2021-02-11T16:30:19.010Z",
			"status": -1,
			"backupNumber": 10,
			"duration": 2530,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0011].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-02-18T06:10:56.339Z",
			"endingDate": "2021-02-18T06:10:56.339Z",
			"scheduledDate": "2021-02-18T06:10:56.339Z",
			"status": -1,
			"backupNumber": 11,
			"duration": 2099,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0012].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-02-24T08:08:24.370Z",
			"endingDate": "2021-02-24T08:08:24.370Z",
			"scheduledDate": "2021-02-24T08:08:24.370Z",
			"status": -1,
			"backupNumber": 12,
			"duration": 3865,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0013].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-02-25T16:20:57.859Z",
			"endingDate": "2021-02-25T16:20:57.859Z",
			"scheduledDate": "2021-02-25T16:20:57.859Z",
			"status": -1,
			"backupNumber": 13,
			"duration": 2830,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0014].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-02-26T14:49:35.479Z",
			"endingDate": "2021-02-26T14:49:35.479Z",
			"scheduledDate": "2021-02-26T14:49:35.479Z",
			"status": -1,
			"backupNumber": 14,
			"duration": 2724,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0015].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-03-03T08:21:57.534Z",
			"endingDate": "2021-03-03T08:21:57.534Z",
			"scheduledDate": "2021-03-03T08:21:57.534Z",
			"status": -1,
			"backupNumber": 15,
			"duration": 2678,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0016].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-03-09T17:25:46.101Z",
			"endingDate": "2021-03-09T17:25:46.101Z",
			"scheduledDate": "2021-03-09T17:25:46.101Z",
			"status": -1,
			"backupNumber": 16,
			"duration": 2747,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0017].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-03-11T16:55:17.731Z",
			"endingDate": "2021-03-11T16:55:17.731Z",
			"scheduledDate": "2021-03-11T16:55:17.731Z",
			"status": -1,
			"backupNumber": 17,
			"duration": 4027,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0018].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-03-20T10:26:02.314Z",
			"endingDate": "2021-03-20T10:26:02.314Z",
			"scheduledDate": "2021-03-20T10:26:02.314Z",
			"status": -1,
			"backupNumber": 18,
			"duration": 4386,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0019].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-04-07T16:19:44.011Z",
			"endingDate": "2021-04-07T16:19:44.011Z",
			"scheduledDate": "2021-04-07T16:19:44.011Z",
			"status": -1,
			"backupNumber": 19,
			"duration": 3701,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0020].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-04-27T11:15:39.688Z",
			"endingDate": "2021-04-27T11:15:39.688Z",
			"scheduledDate": "2021-04-27T11:15:39.688Z",
			"status": -1,
			"backupNumber": 20,
			"duration": 2850,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0021].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-04-29T06:14:20.221Z",
			"endingDate": "2021-04-29T06:14:20.221Z",
			"scheduledDate": "2021-04-29T06:14:20.221Z",
			"status": -1,
			"backupNumber": 21,
			"duration": 4588,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0022].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-05-05T15:57:53.046Z",
			"endingDate": "2021-05-05T15:57:53.046Z",
			"scheduledDate": "2021-05-05T15:57:53.046Z",
			"status": -1,
			"backupNumber": 22,
			"duration": 3089,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0023].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-05-11T05:23:53.551Z",
			"endingDate": "2021-05-11T05:23:53.551Z",
			"scheduledDate": "2021-05-11T05:23:53.551Z",
			"status": -1,
			"backupNumber": 23,
			"duration": 3540,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0024].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-05-16T08:09:01.248Z",
			"endingDate": "2021-05-16T08:09:01.248Z",
			"scheduledDate": "2021-05-16T08:09:01.248Z",
			"status": -1,
			"backupNumber": 24,
			"duration": 3118,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0025].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-05-25T05:58:24.629Z",
			"endingDate": "2021-05-25T05:58:24.629Z",
			"scheduledDate": "2021-05-25T05:58:24.629Z",
			"status": -1,
			"backupNumber": 25,
			"duration": 2789,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0026].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-05-26T06:05:23.815Z",
			"endingDate": "2021-05-26T06:05:23.815Z",
			"scheduledDate": "2021-05-26T06:05:23.815Z",
			"status": -1,
			"backupNumber": 26,
			"duration": 3583,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0027].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-05-31T12:37:18.621Z",
			"endingDate": "2021-05-31T12:37:18.621Z",
			"scheduledDate": "2021-05-31T12:37:18.621Z",
			"status": -1,
			"backupNumber": 27,
			"duration": 4107,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0028].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-06-01T07:31:23.446Z",
			"endingDate": "2021-06-01T07:31:23.446Z",
			"scheduledDate": "2021-06-01T07:31:23.446Z",
			"status": -1,
			"backupNumber": 28,
			"duration": 2216,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0029].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-06-04T06:58:37.391Z",
			"endingDate": "2021-06-04T06:58:37.391Z",
			"scheduledDate": "2021-06-04T06:58:37.391Z",
			"status": -1,
			"backupNumber": 29,
			"duration": 4155,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0030].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-06-15T15:31:51.387Z",
			"endingDate": "2021-06-15T15:31:51.387Z",
			"scheduledDate": "2021-06-15T15:31:51.387Z",
			"status": -1,
			"backupNumber": 30,
			"duration": 3382,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0031].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-06-19T10:17:05.575Z",
			"endingDate": "2021-06-19T10:17:05.575Z",
			"scheduledDate": "2021-06-19T10:17:05.575Z",
			"status": -1,
			"backupNumber": 31,
			"duration": 3542,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0032].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-06-24T06:03:33.596Z",
			"endingDate": "2021-06-24T06:03:33.596Z",
			"scheduledDate": "2021-06-24T06:03:33.596Z",
			"status": -1,
			"backupNumber": 32,
			"duration": 2541,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0033].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-07-01T05:06:39.016Z",
			"endingDate": "2021-07-01T05:06:39.016Z",
			"scheduledDate": "2021-07-01T05:06:39.016Z",
			"status": -1,
			"backupNumber": 33,
			"duration": 3754,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0034].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-07-03T03:31:53.441Z",
			"endingDate": "2021-07-03T03:31:53.441Z",
			"scheduledDate": "2021-07-03T03:31:53.441Z",
			"status": -1,
			"backupNumber": 34,
			"duration": 4782,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0035].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-07-07T08:09:46.289Z",
			"endingDate": "2021-07-07T08:09:46.289Z",
			"scheduledDate": "2021-07-07T08:09:46.289Z",
			"status": -1,
			"backupNumber": 35,
			"duration": 2201,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0036].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-07-14T08:43:33.564Z",
			"endingDate": "2021-07-14T08:43:33.564Z",
			"scheduledDate": "2021-07-14T08:43:33.564Z",
			"status": -1,
			"backupNumber": 36,
			"duration": 2438,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0037].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-07-20T10:36:44.319Z",
			"endingDate": "2021-07-20T10:36:44.319Z",
			"scheduledDate": "2021-07-20T10:36:44.319Z",
			"status": -1,
			"backupNumber": 37,
			"duration": 2543,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0038].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-07-22T15:11:04.048Z",
			"endingDate": "2021-07-22T15:11:04.048Z",
			"scheduledDate": "2021-07-22T15:11:04.048Z",
			"status": -1,
			"backupNumber": 38,
			"duration": 5068,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0039].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-07-28T16:14:16.133Z",
			"endingDate": "2021-07-28T16:14:16.133Z",
			"scheduledDate": "2021-07-28T16:14:16.133Z",
			"status": -1,
			"backupNumber": 39,
			"duration": 3189,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0040].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-08-02T05:36:04.262Z",
			"endingDate": "2021-08-02T05:36:04.262Z",
			"scheduledDate": "2021-08-02T05:36:04.262Z",
			"status": -1,
			"backupNumber": 40,
			"duration": 3002,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0041].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-08-06T11:50:25.750Z",
			"endingDate": "2021-08-06T11:50:25.750Z",
			"scheduledDate": "2021-08-06T11:50:25.750Z",
			"status": -1,
			"backupNumber": 41,
			"duration": 3075,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0042].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-08-11T13:29:45.990Z",
			"endingDate": "2021-08-11T13:29:45.990Z",
			"scheduledDate": "2021-08-11T13:29:45.990Z",
			"status": -1,
			"backupNumber": 42,
			"duration": 3638,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0043].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-08-17T09:19:25.099Z",
			"endingDate": "2021-08-17T09:19:25.099Z",
			"scheduledDate": "2021-08-17T09:19:25.099Z",
			"status": -1,
			"backupNumber": 43,
			"duration": 4288,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0044].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-08-18T08:17:40.930Z",
			"endingDate": "2021-08-18T08:17:40.930Z",
			"scheduledDate": "2021-08-18T08:17:40.930Z",
			"status": -1,
			"backupNumber": 44,
			"duration": 4725,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0045].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-08-24T05:48:32.090Z",
			"endingDate": "2021-08-24T05:48:32.090Z",
			"scheduledDate": "2021-08-24T05:48:32.090Z",
			"status": -1,
			"backupNumber": 45,
			"duration": 2428,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0046].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-08-28T05:24:30.442Z",
			"endingDate": "2021-08-28T05:24:30.442Z",
			"scheduledDate": "2021-08-28T05:24:30.442Z",
			"status": -1,
			"backupNumber": 46,
			"duration": 4336,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0047].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-08-31T06:21:49.247Z",
			"endingDate": "2021-08-31T06:21:49.247Z",
			"scheduledDate": "2021-08-31T06:21:49.247Z",
			"status": -1,
			"backupNumber": 47,
			"duration": 4333,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0048].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-09-01T04:51:26.801Z",
			"endingDate": "2021-09-01T04:51:26.801Z",
			"scheduledDate": "2021-09-01T04:51:26.801Z",
			"status": -1,
			"backupNumber": 48,
			"duration": 3377,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0049].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-09-03T06:41:50.894Z",
			"endingDate": "2021-09-03T06:41:50.894Z",
			"scheduledDate": "2021-09-03T06:41:50.894Z",
			"status": -1,
			"backupNumber": 49,
			"duration": 2529,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0050].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-09-05T16:16:00.799Z",
			"endingDate": "2021-09-05T16:16:00.799Z",
			"scheduledDate": "2021-09-05T16:16:00.799Z",
			"status": -1,
			"backupNumber": 50,
			"duration": 3872,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0051].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-09-11T04:56:14.834Z",
			"endingDate": "2021-09-11T04:56:14.834Z",
			"scheduledDate": "2021-09-11T04:56:14.834Z",
			"status": -1,
			"backupNumber": 51,
			"duration": 3003,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0052].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-09-15T09:35:13.426Z",
			"endingDate": "2021-09-15T09:35:13.426Z",
			"scheduledDate": "2021-09-15T09:35:13.426Z",
			"status": -1,
			"backupNumber": 52,
			"duration": 4504,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0053].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-09-19T15:40:23.610Z",
			"endingDate": "2021-09-19T15:40:23.610Z",
			"scheduledDate": "2021-09-19T15:40:23.610Z",
			"status": -1,
			"backupNumber": 53,
			"duration": 4192,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0054].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-09-23T05:04:08.800Z",
			"endingDate": "2021-09-23T05:04:08.800Z",
			"scheduledDate": "2021-09-23T05:04:08.800Z",
			"status": -1,
			"backupNumber": 54,
			"duration": 3997,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0055].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-09-27T06:33:43.162Z",
			"endingDate": "2021-09-27T06:33:43.162Z",
			"scheduledDate": "2021-09-27T06:33:43.162Z",
			"status": -1,
			"backupNumber": 55,
			"duration": 4851,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0056].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-09-28T12:13:38.522Z",
			"endingDate": "2021-09-28T12:13:38.522Z",
			"scheduledDate": "2021-09-28T12:13:38.522Z",
			"status": -1,
			"backupNumber": 56,
			"duration": 4292,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0057].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-09-30T05:16:13.674Z",
			"endingDate": "2021-09-30T05:16:13.674Z",
			"scheduledDate": "2021-09-30T05:16:13.674Z",
			"status": -1,
			"backupNumber": 57,
			"duration": 2351,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0058].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-01T14:34:23.993Z",
			"endingDate": "2021-10-01T14:34:23.993Z",
			"scheduledDate": "2021-10-01T14:34:23.993Z",
			"status": -1,
			"backupNumber": 58,
			"duration": 2373,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0059].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-04T16:19:45.373Z",
			"endingDate": "2021-10-04T16:19:45.373Z",
			"scheduledDate": "2021-10-04T16:19:45.373Z",
			"status": -1,
			"backupNumber": 59,
			"duration": 3992,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0060].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-05T16:03:10.605Z",
			"endingDate": "2021-10-05T16:03:10.605Z",
			"scheduledDate": "2021-10-05T16:03:10.605Z",
			"status": -1,
			"backupNumber": 60,
			"duration": 3014,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0061].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-07T04:54:32.465Z",
			"endingDate": "2021-10-07T04:54:32.465Z",
			"scheduledDate": "2021-10-07T04:54:32.465Z",
			"status": -1,
			"backupNumber": 61,
			"duration": 3642,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0062].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-08T05:57:06.101Z",
			"endingDate": "2021-10-08T05:57:06.101Z",
			"scheduledDate": "2021-10-08T05:57:06.101Z",
			"status": -1,
			"backupNumber": 62,
			"duration": 4473,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0063].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-09T07:41:51.647Z",
			"endingDate": "2021-10-09T07:41:51.647Z",
			"scheduledDate": "2021-10-09T07:41:51.647Z",
			"status": -1,
			"backupNumber": 63,
			"duration": 3309,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0064].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-11T09:20:26.312Z",
			"endingDate": "2021-10-11T09:20:26.312Z",
			"scheduledDate": "2021-10-11T09:20:26.312Z",
			"status": -1,
			"backupNumber": 64,
			"duration": 4255,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0065].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-13T10:58:31.325Z",
			"endingDate": "2021-10-13T10:58:31.325Z",
			"scheduledDate": "2021-10-13T10:58:31.325Z",
			"status": -1,
			"backupNumber": 65,
			"duration": 4921,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0066].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-15T07:03:34.417Z",
			"endingDate": "2021-10-15T07:03:34.417Z",
			"scheduledDate": "2021-10-15T07:03:34.417Z",
			"status": -1,
			"backupNumber": 66,
			"duration": 3279,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0067].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-21T05:04:27.455Z",
			"endingDate": "2021-10-21T05:04:27.455Z",
			"scheduledDate": "2021-10-21T05:04:27.455Z",
			"status": -1,
			"backupNumber": 67,
			"duration": 3300,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0068].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-24T06:39:09.674Z",
			"endingDate": "2021-10-24T06:39:09.674Z",
			"scheduledDate": "2021-10-24T06:39:09.674Z",
			"status": -1,
			"backupNumber": 68,
			"duration": 5602,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0069].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-25T10:57:07.769Z",
			"endingDate": "2021-10-25T10:57:07.769Z",
			"scheduledDate": "2021-10-25T10:57:07.769Z",
			"status": -1,
			"backupNumber": 69,
			"duration": 6167,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0070].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-26T08:04:37.085Z",
			"endingDate": "2021-10-26T08:04:37.085Z",
			"scheduledDate": "2021-10-26T08:04:37.085Z",
			"status": -1,
			"backupNumber": 70,
			"duration": 3512,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0071].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2021-10-27T04:50:19.806Z",
			"endingDate": "2021-10-27T04:50:19.806Z",
			"scheduledDate": "2021-10-27T04:50:19.806Z",
			"status": -1,
			"backupNumber": 71,
			"duration": 5798,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0072].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-02-21T15:00:35.250Z",
			"endingDate": "2022-02-21T15:00:35.250Z",
			"scheduledDate": "2022-02-21T15:00:35.250Z",
			"status": -1,
			"backupNumber": 72,
			"duration": 271,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0073].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-02-23T17:22:14.648Z",
			"endingDate": "2022-02-23T17:22:14.648Z",
			"scheduledDate": "2022-02-23T17:22:14.648Z",
			"status": -1,
			"backupNumber": 73,
			"duration": 285,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 11.8.10:Daten Bellut[0074].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2020-10.app:Contents:Database:msuFAKT 2020-10.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-02-24T14:11:23.358Z",
			"endingDate": "2022-02-24T14:11:23.358Z",
			"scheduledDate": "2022-02-24T14:11:23.358Z",
			"status": -1,
			"backupNumber": 74,
			"duration": 267,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		}
	]
}