﻿{
	"SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ": [
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0001].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-02-25T10:21:03.900Z",
			"endingDate": "2022-02-25T10:21:03.900Z",
			"scheduledDate": "2022-02-25T10:21:03.900Z",
			"status": -1,
			"backupNumber": 1,
			"duration": 277,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0002].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-03-08T17:06:45.643Z",
			"endingDate": "2022-03-08T17:06:45.643Z",
			"scheduledDate": "2022-03-08T17:06:45.643Z",
			"status": -1,
			"backupNumber": 2,
			"duration": 304,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0003].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-03-15T17:12:59.670Z",
			"endingDate": "2022-03-15T17:12:59.670Z",
			"scheduledDate": "2022-03-15T17:12:59.670Z",
			"status": -1,
			"backupNumber": 3,
			"duration": 318,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0004].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-03-17T09:07:03.753Z",
			"endingDate": "2022-03-17T09:07:03.753Z",
			"scheduledDate": "2022-03-17T09:07:03.753Z",
			"status": -1,
			"backupNumber": 4,
			"duration": 296,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0005].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-03-18T16:25:35.342Z",
			"endingDate": "2022-03-18T16:25:35.342Z",
			"scheduledDate": "2022-03-18T16:25:35.342Z",
			"status": -1,
			"backupNumber": 5,
			"duration": 297,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0006].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-03-21T17:28:51.178Z",
			"endingDate": "2022-03-21T17:28:51.178Z",
			"scheduledDate": "2022-03-21T17:28:51.178Z",
			"status": -1,
			"backupNumber": 6,
			"duration": 421,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0007].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-03-22T07:00:59.599Z",
			"endingDate": "2022-03-22T07:00:59.599Z",
			"scheduledDate": "2022-03-22T07:00:59.599Z",
			"status": -1,
			"backupNumber": 7,
			"duration": 285,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0008].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-03-24T07:17:28.183Z",
			"endingDate": "2022-03-24T07:17:28.183Z",
			"scheduledDate": "2022-03-24T07:17:28.183Z",
			"status": -1,
			"backupNumber": 8,
			"duration": 306,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0009].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-03-28T05:41:46.198Z",
			"endingDate": "2022-03-28T05:41:46.198Z",
			"scheduledDate": "2022-03-28T05:41:46.198Z",
			"status": -1,
			"backupNumber": 9,
			"duration": 289,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0010].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-03-30T05:33:35.892Z",
			"endingDate": "2022-03-30T05:33:35.892Z",
			"scheduledDate": "2022-03-30T05:33:35.892Z",
			"status": -1,
			"backupNumber": 10,
			"duration": 358,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0011].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-04-03T08:50:17.529Z",
			"endingDate": "2022-04-03T08:50:17.529Z",
			"scheduledDate": "2022-04-03T08:50:17.529Z",
			"status": -1,
			"backupNumber": 11,
			"duration": 290,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0012].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-04-04T15:33:05.288Z",
			"endingDate": "2022-04-04T15:33:05.288Z",
			"scheduledDate": "2022-04-04T15:33:05.288Z",
			"status": -1,
			"backupNumber": 12,
			"duration": 298,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0013].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-04-05T05:37:33.519Z",
			"endingDate": "2022-04-05T05:37:33.519Z",
			"scheduledDate": "2022-04-05T05:37:33.519Z",
			"status": -1,
			"backupNumber": 13,
			"duration": 311,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0014].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-04-08T11:02:01.975Z",
			"endingDate": "2022-04-08T11:02:01.975Z",
			"scheduledDate": "2022-04-08T11:02:01.975Z",
			"status": -1,
			"backupNumber": 14,
			"duration": 311,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0015].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-04-13T09:43:34.128Z",
			"endingDate": "2022-04-13T09:43:34.128Z",
			"scheduledDate": "2022-04-13T09:43:34.128Z",
			"status": -1,
			"backupNumber": 15,
			"duration": 428,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0016].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-04-15T10:32:02.638Z",
			"endingDate": "2022-04-15T10:32:02.638Z",
			"scheduledDate": "2022-04-15T10:32:02.638Z",
			"status": -1,
			"backupNumber": 16,
			"duration": 387,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0017].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-04-20T05:14:33.573Z",
			"endingDate": "2022-04-20T05:14:33.573Z",
			"scheduledDate": "2022-04-20T05:14:33.573Z",
			"status": -1,
			"backupNumber": 17,
			"duration": 319,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0018].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-04-27T05:58:52.245Z",
			"endingDate": "2022-04-27T05:58:52.245Z",
			"scheduledDate": "2022-04-27T05:58:52.245Z",
			"status": -1,
			"backupNumber": 18,
			"duration": 329,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0019].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-05-01T15:53:30.560Z",
			"endingDate": "2022-05-01T15:53:30.560Z",
			"scheduledDate": "2022-05-01T15:53:30.560Z",
			"status": -1,
			"backupNumber": 19,
			"duration": 318,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0020].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-05-04T06:20:49.424Z",
			"endingDate": "2022-05-04T06:20:49.424Z",
			"scheduledDate": "2022-05-04T06:20:49.424Z",
			"status": -1,
			"backupNumber": 20,
			"duration": 325,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0021].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-05-06T14:14:46.280Z",
			"endingDate": "2022-05-06T14:14:46.280Z",
			"scheduledDate": "2022-05-06T14:14:46.280Z",
			"status": -1,
			"backupNumber": 21,
			"duration": 318,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0022].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-05-10T15:40:36.423Z",
			"endingDate": "2022-05-10T15:40:36.423Z",
			"scheduledDate": "2022-05-10T15:40:36.423Z",
			"status": -1,
			"backupNumber": 22,
			"duration": 347,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0023].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-05-17T15:24:01.466Z",
			"endingDate": "2022-05-17T15:24:01.466Z",
			"scheduledDate": "2022-05-17T15:24:01.466Z",
			"status": -1,
			"backupNumber": 23,
			"duration": 327,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0024].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-05-19T06:19:08.986Z",
			"endingDate": "2022-05-19T06:19:08.986Z",
			"scheduledDate": "2022-05-19T06:19:08.986Z",
			"status": -1,
			"backupNumber": 24,
			"duration": 335,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0025].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-05-21T07:00:28.285Z",
			"endingDate": "2022-05-21T07:00:28.285Z",
			"scheduledDate": "2022-05-21T07:00:28.285Z",
			"status": -1,
			"backupNumber": 25,
			"duration": 598,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0026].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-05-23T15:53:57.778Z",
			"endingDate": "2022-05-23T15:53:57.778Z",
			"scheduledDate": "2022-05-23T15:53:57.778Z",
			"status": -1,
			"backupNumber": 26,
			"duration": 303,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0027].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-05-24T16:18:15.164Z",
			"endingDate": "2022-05-24T16:18:15.164Z",
			"scheduledDate": "2022-05-24T16:18:15.164Z",
			"status": -1,
			"backupNumber": 27,
			"duration": 539,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0028].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-05-31T09:26:32.453Z",
			"endingDate": "2022-05-31T09:26:32.453Z",
			"scheduledDate": "2022-05-31T09:26:32.453Z",
			"status": -1,
			"backupNumber": 28,
			"duration": 359,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0029].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-06-03T08:39:27.648Z",
			"endingDate": "2022-06-03T08:39:27.648Z",
			"scheduledDate": "2022-06-03T08:39:27.648Z",
			"status": -1,
			"backupNumber": 29,
			"duration": 352,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0030].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-06-15T05:24:47.045Z",
			"endingDate": "2022-06-15T05:24:47.045Z",
			"scheduledDate": "2022-06-15T05:24:47.045Z",
			"status": -1,
			"backupNumber": 30,
			"duration": 485,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0031].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-06-21T10:41:17.149Z",
			"endingDate": "2022-06-21T10:41:17.149Z",
			"scheduledDate": "2022-06-21T10:41:17.149Z",
			"status": -1,
			"backupNumber": 31,
			"duration": 464,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0032].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-06-27T13:10:31.831Z",
			"endingDate": "2022-06-27T13:10:31.831Z",
			"scheduledDate": "2022-06-27T13:10:31.831Z",
			"status": -1,
			"backupNumber": 32,
			"duration": 450,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		},
		{
			"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.02:Daten Bellut[0033].4BK",
			"structurePath": "SSD:Applications:msuFAKT 2022-02.app:Contents:Database:msuFAKT 2022-02.4DZ",
			"dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD",
			"logPath": "",
			"startingDate": "2022-06-30T13:51:13.284Z",
			"endingDate": "2022-06-30T13:51:13.284Z",
			"scheduledDate": "2022-06-30T13:51:13.284Z",
			"status": -1,
			"backupNumber": 33,
			"duration": 416,
			"attachments": [
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx",
					"found": true
				},
				{
					"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match",
					"found": true
				}
			]
		}
	]
}