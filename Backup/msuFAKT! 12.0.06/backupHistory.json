{"SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ": [{"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Daten <PERSON>[0001].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-07-01T06:11:29.101Z", "endingDate": "2022-07-01T06:11:29.101Z", "scheduledDate": "2022-07-01T06:11:29.101Z", "backupNumber": 1, "duration": 348, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Daten <PERSON>[0002].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-07-04T16:20:10.364Z", "endingDate": "2022-07-04T16:20:10.364Z", "scheduledDate": "2022-07-04T16:20:10.364Z", "backupNumber": 2, "duration": 560, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Daten <PERSON>[0003].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-07-07T03:47:20.901Z", "endingDate": "2022-07-07T03:47:20.901Z", "scheduledDate": "2022-07-07T03:47:20.901Z", "backupNumber": 3, "duration": 514, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Daten <PERSON>[0004].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-07-11T14:02:27.648Z", "endingDate": "2022-07-11T14:02:27.648Z", "scheduledDate": "2022-07-11T14:02:27.648Z", "backupNumber": 4, "duration": 477, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Daten <PERSON>[0005].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-07-14T17:33:22.960Z", "endingDate": "2022-07-14T17:33:22.960Z", "scheduledDate": "2022-07-14T17:33:22.960Z", "backupNumber": 5, "duration": 356, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Daten <PERSON>[0006].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-07-19T06:00:16.381Z", "endingDate": "2022-07-19T06:00:16.381Z", "scheduledDate": "2022-07-19T06:00:16.381Z", "backupNumber": 6, "duration": 373, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Daten <PERSON>[0007].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-08-05T11:12:04.334Z", "endingDate": "2022-08-05T11:12:04.334Z", "scheduledDate": "2022-08-05T11:12:04.334Z", "backupNumber": 7, "duration": 689, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Daten <PERSON>[0008].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-08-07T16:41:20.180Z", "endingDate": "2022-08-07T16:41:20.180Z", "scheduledDate": "2022-08-07T16:41:20.180Z", "backupNumber": 8, "duration": 374, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Daten <PERSON>[0009].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-08-15T05:53:32.614Z", "endingDate": "2022-08-15T05:53:32.614Z", "scheduledDate": "2022-08-15T05:53:32.614Z", "backupNumber": 9, "duration": 712, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0010].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-08-18T05:17:33.657Z", "endingDate": "2022-08-18T05:17:33.657Z", "scheduledDate": "2022-08-18T05:17:33.657Z", "backupNumber": 10, "duration": 404, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0011].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-08-22T07:02:18.790Z", "endingDate": "2022-08-22T07:02:18.790Z", "scheduledDate": "2022-08-22T07:02:18.790Z", "backupNumber": 11, "duration": 830, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0012].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-08-24T05:40:30.043Z", "endingDate": "2022-08-24T05:40:30.043Z", "scheduledDate": "2022-08-24T05:40:30.043Z", "backupNumber": 12, "duration": 391, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0013].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-08-25T11:09:11.902Z", "endingDate": "2022-08-25T11:09:11.902Z", "scheduledDate": "2022-08-25T11:09:11.902Z", "backupNumber": 13, "duration": 411, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0014].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-08-26T16:09:45.489Z", "endingDate": "2022-08-26T16:09:45.489Z", "scheduledDate": "2022-08-26T16:09:45.489Z", "backupNumber": 14, "duration": 467, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0015].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-08-31T06:18:01.175Z", "endingDate": "2022-08-31T06:18:01.175Z", "scheduledDate": "2022-08-31T06:18:01.175Z", "backupNumber": 15, "duration": 417, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0016].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-02T06:14:20.677Z", "endingDate": "2022-09-02T06:14:20.677Z", "scheduledDate": "2022-09-02T06:14:20.677Z", "backupNumber": 16, "duration": 418, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0017].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-05T15:44:26.373Z", "endingDate": "2022-09-05T15:44:26.373Z", "scheduledDate": "2022-09-05T15:44:26.373Z", "backupNumber": 17, "duration": 429, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0018].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-07T05:07:02.640Z", "endingDate": "2022-09-07T05:07:02.640Z", "scheduledDate": "2022-09-07T05:07:02.640Z", "backupNumber": 18, "duration": 465, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0019].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-08T09:42:33.016Z", "endingDate": "2022-09-08T09:42:33.016Z", "scheduledDate": "2022-09-08T09:42:33.016Z", "backupNumber": 19, "duration": 389, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0020].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-09T13:04:07.370Z", "endingDate": "2022-09-09T13:04:07.370Z", "scheduledDate": "2022-09-09T13:04:07.370Z", "backupNumber": 20, "duration": 370, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0021].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-10T07:07:17.817Z", "endingDate": "2022-09-10T07:07:17.817Z", "scheduledDate": "2022-09-10T07:07:17.817Z", "backupNumber": 21, "duration": 436, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0022].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-13T06:00:38.686Z", "endingDate": "2022-09-13T06:00:38.686Z", "scheduledDate": "2022-09-13T06:00:38.686Z", "backupNumber": 22, "duration": 425, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0023].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-15T05:24:42.055Z", "endingDate": "2022-09-15T05:24:42.055Z", "scheduledDate": "2022-09-15T05:24:42.055Z", "backupNumber": 23, "duration": 459, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0024].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-17T07:53:58.631Z", "endingDate": "2022-09-17T07:53:58.631Z", "scheduledDate": "2022-09-17T07:53:58.631Z", "backupNumber": 24, "duration": 586, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0025].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-20T05:35:48.633Z", "endingDate": "2022-09-20T05:35:48.633Z", "scheduledDate": "2022-09-20T05:35:48.633Z", "backupNumber": 25, "duration": 442, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0026].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-21T04:51:28.643Z", "endingDate": "2022-09-21T04:51:28.643Z", "scheduledDate": "2022-09-21T04:51:28.643Z", "backupNumber": 26, "duration": 407, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0027].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-22T05:54:01.753Z", "endingDate": "2022-09-22T05:54:01.753Z", "scheduledDate": "2022-09-22T05:54:01.753Z", "backupNumber": 27, "duration": 370, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0028].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-23T04:47:05.090Z", "endingDate": "2022-09-23T04:47:05.090Z", "scheduledDate": "2022-09-23T04:47:05.090Z", "backupNumber": 28, "duration": 513, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0029].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-26T05:05:07.959Z", "endingDate": "2022-09-26T05:05:07.959Z", "scheduledDate": "2022-09-26T05:05:07.959Z", "backupNumber": 29, "duration": 392, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0030].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-27T05:43:12.576Z", "endingDate": "2022-09-27T05:43:12.576Z", "scheduledDate": "2022-09-27T05:43:12.576Z", "backupNumber": 30, "duration": 341, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0031].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-28T05:15:36.261Z", "endingDate": "2022-09-28T05:15:36.261Z", "scheduledDate": "2022-09-28T05:15:36.261Z", "backupNumber": 31, "duration": 403, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0032].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-09-30T04:45:12.704Z", "endingDate": "2022-09-30T04:45:12.704Z", "scheduledDate": "2022-09-30T04:45:12.704Z", "backupNumber": 32, "duration": 376, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0033].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-10-05T08:59:19.454Z", "endingDate": "2022-10-05T08:59:19.454Z", "scheduledDate": "2022-10-05T08:59:19.454Z", "backupNumber": 33, "duration": 321, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0034].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-10-13T05:25:15.538Z", "endingDate": "2022-10-13T05:25:15.538Z", "scheduledDate": "2022-10-13T05:25:15.538Z", "backupNumber": 34, "duration": 416, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0035].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-10-16T15:52:18.328Z", "endingDate": "2022-10-16T15:52:18.328Z", "scheduledDate": "2022-10-16T15:52:18.328Z", "backupNumber": 35, "duration": 541, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0036].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-10-19T06:04:32.231Z", "endingDate": "2022-10-19T06:04:32.231Z", "scheduledDate": "2022-10-19T06:04:32.231Z", "backupNumber": 36, "duration": 454, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0037].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-10-21T06:49:47.422Z", "endingDate": "2022-10-21T06:49:47.422Z", "scheduledDate": "2022-10-21T06:49:47.422Z", "backupNumber": 37, "duration": 376, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0038].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-10-24T15:42:41.646Z", "endingDate": "2022-10-24T15:42:41.646Z", "scheduledDate": "2022-10-24T15:42:41.646Z", "backupNumber": 38, "duration": 377, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0039].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-10-26T05:02:48.075Z", "endingDate": "2022-10-26T05:02:48.075Z", "scheduledDate": "2022-10-26T05:02:48.075Z", "backupNumber": 39, "duration": 578, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0040].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-10-27T06:01:20.653Z", "endingDate": "2022-10-27T06:01:20.653Z", "scheduledDate": "2022-10-27T06:01:20.653Z", "backupNumber": 40, "duration": 400, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:Date<PERSON>[0041].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-10-30T17:21:06.770Z", "endingDate": "2022-10-30T17:21:06.770Z", "scheduledDate": "2022-10-30T17:21:06.770Z", "backupNumber": 41, "duration": 422, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.0.06:<PERSON><PERSON>[0042].4BK", "structurePath": "SSD:Applications:msuFAKT 2022-06.app:Contents:Database:msuFAKT 2022-06.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-11-03T04:51:49.012Z", "endingDate": "2022-11-03T04:51:49.012Z", "scheduledDate": "2022-11-03T04:51:49.012Z", "backupNumber": 42, "duration": 6738, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}]}