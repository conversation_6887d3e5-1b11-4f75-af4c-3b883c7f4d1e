{"SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ": [{"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Daten <PERSON>[0001].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-07-14T16:50:08.022Z", "endingDate": "2023-07-14T16:50:08.022Z", "scheduledDate": "2023-07-14T16:50:08.022Z", "backupNumber": 1, "duration": 473, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Daten <PERSON>[0002].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-07-17T15:50:54.410Z", "endingDate": "2023-07-17T15:50:54.410Z", "scheduledDate": "2023-07-17T15:50:54.410Z", "backupNumber": 2, "duration": 498, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Daten <PERSON>[0003].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-07-19T09:24:04.890Z", "endingDate": "2023-07-19T09:24:04.890Z", "scheduledDate": "2023-07-19T09:24:04.890Z", "backupNumber": 3, "duration": 377, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Daten <PERSON>[0004].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-07-20T15:44:57.583Z", "endingDate": "2023-07-20T15:44:57.583Z", "scheduledDate": "2023-07-20T15:44:57.583Z", "backupNumber": 4, "duration": 583, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Daten <PERSON>[0005].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-07-25T15:52:56.997Z", "endingDate": "2023-07-25T15:52:56.997Z", "scheduledDate": "2023-07-25T15:52:56.997Z", "backupNumber": 5, "duration": 489, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Daten <PERSON>[0006].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-07-25T15:53:05.134Z", "endingDate": "2023-07-25T15:53:05.134Z", "scheduledDate": "2023-07-25T15:53:05.134Z", "backupNumber": 6, "duration": 269, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Daten <PERSON>[0007].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-07-26T09:22:45.427Z", "endingDate": "2023-07-26T09:22:45.427Z", "scheduledDate": "2023-07-26T09:22:45.427Z", "backupNumber": 7, "duration": 1695, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Daten <PERSON>[0008].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-07-31T17:15:19.420Z", "endingDate": "2023-07-31T17:15:19.420Z", "scheduledDate": "2023-07-31T17:15:19.420Z", "backupNumber": 8, "duration": 496, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Daten <PERSON>[0009].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-08-01T15:52:45.434Z", "endingDate": "2023-08-01T15:52:45.434Z", "scheduledDate": "2023-08-01T15:52:45.434Z", "backupNumber": 9, "duration": 446, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Date<PERSON>[0010].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-08-03T07:43:06.862Z", "endingDate": "2023-08-03T07:43:06.862Z", "scheduledDate": "2023-08-03T07:43:06.862Z", "backupNumber": 10, "duration": 441, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Date<PERSON>[0011].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-08-09T15:29:31.312Z", "endingDate": "2023-08-09T15:29:31.312Z", "scheduledDate": "2023-08-09T15:29:31.312Z", "backupNumber": 11, "duration": 431, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Date<PERSON>[0012].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-08-20T11:04:26.134Z", "endingDate": "2023-08-20T11:04:26.134Z", "scheduledDate": "2023-08-20T11:04:26.134Z", "backupNumber": 12, "duration": 586, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Date<PERSON>[0013].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-08-24T10:17:21.216Z", "endingDate": "2023-08-24T10:17:21.216Z", "scheduledDate": "2023-08-24T10:17:21.216Z", "backupNumber": 13, "duration": 579, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Date<PERSON>[0014].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-08-29T12:04:24.965Z", "endingDate": "2023-08-29T12:04:24.965Z", "scheduledDate": "2023-08-29T12:04:24.965Z", "backupNumber": 14, "duration": 993, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}], "Macintosh HD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ": [{"backupPath": "Macintosh HD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.08:Daten <PERSON>[0001].4BK", "structurePath": "Macintosh HD:Applications:msuFAKT 2023-08.app:Contents:Database:msuFAKT 2023-08.4DZ", "dataPath": "Macintosh HD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2025-08-02T13:00:17.405Z", "endingDate": "2025-08-02T13:00:17.405Z", "scheduledDate": "2025-08-02T13:00:17.405Z", "backupNumber": 1, "duration": 273, "attachments": [{"attachmentPath": "Macintosh HD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "Macintosh HD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}]}