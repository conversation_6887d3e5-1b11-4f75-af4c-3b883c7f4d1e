{"SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ": [{"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Daten <PERSON>[0001].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-03-02T16:34:11.054Z", "endingDate": "2023-03-02T16:34:11.054Z", "scheduledDate": "2023-03-02T16:34:11.054Z", "backupNumber": 1, "duration": 470, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Daten <PERSON>[0002].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-03-07T06:05:53.583Z", "endingDate": "2023-03-07T06:05:53.583Z", "scheduledDate": "2023-03-07T06:05:53.583Z", "backupNumber": 2, "duration": 501, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Daten <PERSON>[0003].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-03-15T19:55:23.947Z", "endingDate": "2023-03-15T19:55:23.947Z", "scheduledDate": "2023-03-15T19:55:23.947Z", "backupNumber": 3, "duration": 2063, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Daten <PERSON>[0004].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-03-21T10:16:31.715Z", "endingDate": "2023-03-21T10:16:31.715Z", "scheduledDate": "2023-03-21T10:16:31.715Z", "backupNumber": 4, "duration": 480, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Daten <PERSON>[0005].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-03-27T15:40:28.844Z", "endingDate": "2023-03-27T15:40:28.844Z", "scheduledDate": "2023-03-27T15:40:28.844Z", "backupNumber": 5, "duration": 476, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Daten <PERSON>[0006].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-04-04T07:02:04.047Z", "endingDate": "2023-04-04T07:02:04.047Z", "scheduledDate": "2023-04-04T07:02:04.047Z", "backupNumber": 6, "duration": 572, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Daten <PERSON>[0007].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-04-07T17:12:07.702Z", "endingDate": "2023-04-07T17:12:07.702Z", "scheduledDate": "2023-04-07T17:12:07.702Z", "backupNumber": 7, "duration": 539, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Daten <PERSON>[0008].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-04-15T05:55:18.843Z", "endingDate": "2023-04-15T05:55:18.843Z", "scheduledDate": "2023-04-15T05:55:18.843Z", "backupNumber": 8, "duration": 542, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Daten <PERSON>[0009].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-04-18T15:37:28.651Z", "endingDate": "2023-04-18T15:37:28.651Z", "scheduledDate": "2023-04-18T15:37:28.651Z", "backupNumber": 9, "duration": 532, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Date<PERSON>[0010].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-04-23T16:08:20.522Z", "endingDate": "2023-04-23T16:08:20.522Z", "scheduledDate": "2023-04-23T16:08:20.522Z", "backupNumber": 10, "duration": 566, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:<PERSON><PERSON>[0011].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-05-01T15:03:01.737Z", "endingDate": "2023-05-01T15:03:01.737Z", "scheduledDate": "2023-05-01T15:03:01.737Z", "backupNumber": 11, "duration": 497, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Date<PERSON>[0012].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-05-09T16:02:29.802Z", "endingDate": "2023-05-09T16:02:29.802Z", "scheduledDate": "2023-05-09T16:02:29.802Z", "backupNumber": 12, "duration": 552, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:<PERSON><PERSON>[0013].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-05-12T15:19:05.405Z", "endingDate": "2023-05-12T15:19:05.405Z", "scheduledDate": "2023-05-12T15:19:05.405Z", "backupNumber": 13, "duration": 520, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:<PERSON><PERSON>[0014].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-05-17T16:30:51.578Z", "endingDate": "2023-05-17T16:30:51.578Z", "scheduledDate": "2023-05-17T16:30:51.578Z", "backupNumber": 14, "duration": 432, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Date<PERSON>[0015].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-05-20T11:46:34.144Z", "endingDate": "2023-05-20T11:46:34.144Z", "scheduledDate": "2023-05-20T11:46:34.144Z", "backupNumber": 15, "duration": 428, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Date<PERSON>[0016].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-05-25T14:29:34.073Z", "endingDate": "2023-05-25T14:29:34.073Z", "scheduledDate": "2023-05-25T14:29:34.073Z", "backupNumber": 16, "duration": 525, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Date<PERSON>[0017].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-06-02T14:04:29.682Z", "endingDate": "2023-06-02T14:04:29.682Z", "scheduledDate": "2023-06-02T14:04:29.682Z", "backupNumber": 17, "duration": 512, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Date<PERSON>[0018].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-06-11T17:24:02.518Z", "endingDate": "2023-06-11T17:24:02.518Z", "scheduledDate": "2023-06-11T17:24:02.518Z", "backupNumber": 18, "duration": 834, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Date<PERSON>[0019].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-06-14T11:48:41.519Z", "endingDate": "2023-06-14T11:48:41.519Z", "scheduledDate": "2023-06-14T11:48:41.519Z", "backupNumber": 19, "duration": 1805, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Date<PERSON>[0020].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-06-20T15:25:33.967Z", "endingDate": "2023-06-20T15:25:33.967Z", "scheduledDate": "2023-06-20T15:25:33.967Z", "backupNumber": 20, "duration": 1804, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Date<PERSON>[0021].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-06-22T07:09:04.098Z", "endingDate": "2023-06-22T07:09:04.098Z", "scheduledDate": "2023-06-22T07:09:04.098Z", "backupNumber": 21, "duration": 533, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:<PERSON><PERSON>[0022].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-06-26T18:06:57.210Z", "endingDate": "2023-06-26T18:06:57.210Z", "scheduledDate": "2023-06-26T18:06:57.210Z", "backupNumber": 22, "duration": 479, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Date<PERSON>[0023].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-06-27T11:02:41.759Z", "endingDate": "2023-06-27T11:02:41.759Z", "scheduledDate": "2023-06-27T11:02:41.759Z", "backupNumber": 23, "duration": 555, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:<PERSON><PERSON>[0024].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-06-30T13:16:58.939Z", "endingDate": "2023-06-30T13:16:58.939Z", "scheduledDate": "2023-06-30T13:16:58.939Z", "backupNumber": 24, "duration": 3688, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:<PERSON><PERSON>[0025].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-07-06T07:35:33.577Z", "endingDate": "2023-07-06T07:35:33.577Z", "scheduledDate": "2023-07-06T07:35:33.577Z", "backupNumber": 25, "duration": 636, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Date<PERSON>[0026].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-07-11T15:48:35.306Z", "endingDate": "2023-07-11T15:48:35.306Z", "scheduledDate": "2023-07-11T15:48:35.306Z", "backupNumber": 26, "duration": 465, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:<PERSON><PERSON>[0027].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-07-12T08:05:18.222Z", "endingDate": "2023-07-12T08:05:18.222Z", "scheduledDate": "2023-07-12T08:05:18.222Z", "backupNumber": 27, "duration": 463, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}], "Macintosh HD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ": [{"backupPath": "Macintosh HD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.04:Daten <PERSON>[0001][0001].4BK", "structurePath": "Macintosh HD:Applications:msuFAKT 2023-04.app:Contents:Database:msuFAKT 2023-04.4DZ", "dataPath": "Macintosh HD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut[0001].4DD", "logPath": "", "startingDate": "2024-08-04T11:15:25.604Z", "endingDate": "2024-08-04T11:15:25.604Z", "scheduledDate": "2024-08-04T11:15:25.604Z", "backupNumber": 1, "duration": 264, "attachments": [{"attachmentPath": "Macintosh HD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut[0001].4DIndx", "found": true}, {"attachmentPath": "Macintosh HD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten <PERSON>[0001].Match", "found": true}]}]}