{"SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ": [{"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Daten <PERSON>[0001].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-12-01T17:30:51.823Z", "endingDate": "2022-12-01T17:30:51.823Z", "scheduledDate": "2022-12-01T17:30:51.823Z", "backupNumber": 1, "duration": 534, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Daten <PERSON>[0002].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-12-08T07:23:48.823Z", "endingDate": "2022-12-08T07:23:48.823Z", "scheduledDate": "2022-12-08T07:23:48.823Z", "backupNumber": 2, "duration": 611, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Daten <PERSON>[0003].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-12-09T12:58:21.303Z", "endingDate": "2022-12-09T12:58:21.303Z", "scheduledDate": "2022-12-09T12:58:21.303Z", "backupNumber": 3, "duration": 545, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Daten <PERSON>[0004].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-12-13T06:29:14.484Z", "endingDate": "2022-12-13T06:29:14.484Z", "scheduledDate": "2022-12-13T06:29:14.484Z", "backupNumber": 4, "duration": 463, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Daten <PERSON>[0005].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-12-15T06:58:56.231Z", "endingDate": "2022-12-15T06:58:56.231Z", "scheduledDate": "2022-12-15T06:58:56.231Z", "backupNumber": 5, "duration": 569, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Daten <PERSON>[0006].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-12-17T18:34:35.906Z", "endingDate": "2022-12-17T18:34:35.906Z", "scheduledDate": "2022-12-17T18:34:35.906Z", "backupNumber": 6, "duration": 521, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Daten <PERSON>[0007].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-12-27T13:46:26.640Z", "endingDate": "2022-12-27T13:46:26.640Z", "scheduledDate": "2022-12-27T13:46:26.640Z", "backupNumber": 7, "duration": 530, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Daten <PERSON>[0008].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-01-04T16:55:20.284Z", "endingDate": "2023-01-04T16:55:20.284Z", "scheduledDate": "2023-01-04T16:55:20.284Z", "backupNumber": 8, "duration": 554, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Daten <PERSON>[0009].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-01-10T17:01:27.287Z", "endingDate": "2023-01-10T17:01:27.287Z", "scheduledDate": "2023-01-10T17:01:27.287Z", "backupNumber": 9, "duration": 510, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Date<PERSON>[0010].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-01-11T06:20:38.334Z", "endingDate": "2023-01-11T06:20:38.334Z", "scheduledDate": "2023-01-11T06:20:38.334Z", "backupNumber": 10, "duration": 445, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Date<PERSON>[0011].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-01-15T16:36:48.357Z", "endingDate": "2023-01-15T16:36:48.357Z", "scheduledDate": "2023-01-15T16:36:48.357Z", "backupNumber": 11, "duration": 597, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Date<PERSON>[0012].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-01-17T06:12:01.994Z", "endingDate": "2023-01-17T06:12:01.994Z", "scheduledDate": "2023-01-17T06:12:01.994Z", "backupNumber": 12, "duration": 444, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:<PERSON><PERSON>[0013].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-01-18T06:24:34.841Z", "endingDate": "2023-01-18T06:24:34.841Z", "scheduledDate": "2023-01-18T06:24:34.841Z", "backupNumber": 13, "duration": 438, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Date<PERSON>[0014].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-01-20T14:50:16.060Z", "endingDate": "2023-01-20T14:50:16.060Z", "scheduledDate": "2023-01-20T14:50:16.060Z", "backupNumber": 14, "duration": 454, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Date<PERSON>[0015].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-01-24T10:20:48.613Z", "endingDate": "2023-01-24T10:20:48.613Z", "scheduledDate": "2023-01-24T10:20:48.613Z", "backupNumber": 15, "duration": 499, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Date<PERSON>[0016].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-01-26T16:33:25.755Z", "endingDate": "2023-01-26T16:33:25.755Z", "scheduledDate": "2023-01-26T16:33:25.755Z", "backupNumber": 16, "duration": 506, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.02:Date<PERSON>[0017].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-02.app:Contents:Database:msuFAKT 2023-02.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-01-31T05:40:37.785Z", "endingDate": "2023-01-31T05:40:37.785Z", "scheduledDate": "2023-01-31T05:40:37.785Z", "backupNumber": 17, "duration": 553, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}]}