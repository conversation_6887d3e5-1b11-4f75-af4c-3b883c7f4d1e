{"SSD:Applications:msuFAKT 2023-01.app:Contents:Database:msuFAKT 2023-01.4DZ": [{"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.01:Daten <PERSON>[0001].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-01.app:Contents:Database:msuFAKT 2023-01.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-11-04T06:27:08.290Z", "endingDate": "2022-11-04T06:27:08.290Z", "scheduledDate": "2022-11-04T06:27:08.290Z", "backupNumber": 1, "duration": 391, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.01:Daten <PERSON>[0002].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-01.app:Contents:Database:msuFAKT 2023-01.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-11-06T12:28:03.007Z", "endingDate": "2022-11-06T12:28:03.007Z", "scheduledDate": "2022-11-06T12:28:03.007Z", "backupNumber": 2, "duration": 425, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.01:Daten <PERSON>[0003].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-01.app:Contents:Database:msuFAKT 2023-01.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-11-09T06:07:38.002Z", "endingDate": "2022-11-09T06:07:38.002Z", "scheduledDate": "2022-11-09T06:07:38.002Z", "backupNumber": 3, "duration": 529, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.01:Daten <PERSON>[0004].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-01.app:Contents:Database:msuFAKT 2023-01.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-11-10T11:28:42.145Z", "endingDate": "2022-11-10T11:28:42.145Z", "scheduledDate": "2022-11-10T11:28:42.145Z", "backupNumber": 4, "duration": 365, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.01:Daten <PERSON>[0005].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-01.app:Contents:Database:msuFAKT 2023-01.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-11-11T09:14:53.218Z", "endingDate": "2022-11-11T09:14:53.218Z", "scheduledDate": "2022-11-11T09:14:53.218Z", "backupNumber": 5, "duration": 363, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.01:Daten <PERSON>[0006].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-01.app:Contents:Database:msuFAKT 2023-01.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-11-16T09:58:12.912Z", "endingDate": "2022-11-16T09:58:12.912Z", "scheduledDate": "2022-11-16T09:58:12.912Z", "backupNumber": 6, "duration": 489, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.01:Daten <PERSON>[0007].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-01.app:Contents:Database:msuFAKT 2023-01.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-11-18T17:47:18.396Z", "endingDate": "2022-11-18T17:47:18.396Z", "scheduledDate": "2022-11-18T17:47:18.396Z", "backupNumber": 7, "duration": 436, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.01:Daten <PERSON>[0008].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-01.app:Contents:Database:msuFAKT 2023-01.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2022-11-28T10:14:47.078Z", "endingDate": "2022-11-28T10:14:47.078Z", "scheduledDate": "2022-11-28T10:14:47.078Z", "backupNumber": 8, "duration": 972, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}]}