{"SSD:Applications:msuFAKT 2023-03.app:Contents:Database:msuFAKT 2023-03.4DZ": [{"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.03:Daten <PERSON>[0001].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-03.app:Contents:Database:msuFAKT 2023-03.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-02-03T11:00:28.643Z", "endingDate": "2023-02-03T11:00:28.643Z", "scheduledDate": "2023-02-03T11:00:28.643Z", "backupNumber": 1, "duration": 517, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.03:Daten <PERSON>[0002].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-03.app:Contents:Database:msuFAKT 2023-03.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-02-07T16:47:43.020Z", "endingDate": "2023-02-07T16:47:43.020Z", "scheduledDate": "2023-02-07T16:47:43.020Z", "backupNumber": 2, "duration": 568, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.03:Daten <PERSON>[0003].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-03.app:Contents:Database:msuFAKT 2023-03.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-02-14T13:57:17.984Z", "endingDate": "2023-02-14T13:57:17.984Z", "scheduledDate": "2023-02-14T13:57:17.984Z", "backupNumber": 3, "duration": 552, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.03:Daten <PERSON>[0004].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-03.app:Contents:Database:msuFAKT 2023-03.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-02-18T06:53:13.606Z", "endingDate": "2023-02-18T06:53:13.606Z", "scheduledDate": "2023-02-18T06:53:13.606Z", "backupNumber": 4, "duration": 557, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}, {"backupPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Backup:msuFAKT! 12.1.03:Daten <PERSON>[0005].4BK", "structurePath": "SSD:Applications:msuFAKT 2023-03.app:Contents:Database:msuFAKT 2023-03.4DZ", "dataPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DD", "logPath": "", "startingDate": "2023-02-24T14:41:03.321Z", "endingDate": "2023-02-24T14:41:03.321Z", "scheduledDate": "2023-02-24T14:41:03.321Z", "backupNumber": 5, "duration": 495, "attachments": [{"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.4DIndx", "found": true}, {"attachmentPath": "SSD:Users:svenbellut1:Documents:msuBerlin:Datendateien:Daten Bellut.Match", "found": true}]}]}